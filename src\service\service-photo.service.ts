import { Provide } from '@midwayjs/core';
import { ServicePhoto } from '../entity/service-photo.entity';
import { BaseService } from '../common/BaseService';
import { Order } from '../entity/order.entity';
import { Employee } from '../entity/employee.entity';
import { CustomError } from '../error/custom.error';

@Provide()
export class ServicePhotoService extends BaseService<ServicePhoto> {
  constructor() {
    super('服务照片');
  }

  getModel() {
    return ServicePhoto;
  }

  /**
   * 根据订单ID查找服务照片
   */
  async findByOrderId(orderId: number) {
    return await this.findOne({
      where: { orderId },
      include: [Order, Employee],
    });
  }

  /**
   * 上传服务前照片（支持多张，最多9张）
   */
  async uploadBeforePhotos(
    orderId: number,
    employeeId: number,
    photoUrls: string[]
  ) {
    if (!photoUrls || photoUrls.length === 0) {
      throw new CustomError('照片链接不能为空');
    }

    if (photoUrls.length > 9) {
      throw new CustomError('服务前照片最多只能上传9张');
    }

    // 对新上传的照片进行去重
    const uniqueNewPhotos = [...new Set(photoUrls)];

    // 查找是否已存在记录
    const servicePhoto = await this.findOne({
      where: { orderId, employeeId },
    });

    if (servicePhoto) {
      // 合并现有照片和新照片，并去重
      const existingPhotos = servicePhoto.beforePhotos || [];
      const allPhotos = [...new Set([...existingPhotos, ...uniqueNewPhotos])];

      if (allPhotos.length > 9) {
        throw new CustomError(
          `服务前照片总数不能超过9张，当前已有${existingPhotos.length}张，新增${uniqueNewPhotos.length}张，去重后总计${allPhotos.length}张`
        );
      }

      // 检查是否有新照片被添加
      const newPhotosAdded = allPhotos.length > existingPhotos.length;
      if (!newPhotosAdded) {
        return servicePhoto;
      }

      // 更新现有记录
      await servicePhoto.update({
        beforePhotos: allPhotos,
        beforePhotoTime: new Date(),
      });
      return servicePhoto;
    } else {
      // 创建新记录
      return await this.create({
        orderId,
        employeeId,
        beforePhotos: uniqueNewPhotos,
        beforePhotoTime: new Date(),
      });
    }
  }

  /**
   * 上传单张服务前照片（兼容性方法）
   */
  async uploadBeforePhoto(
    orderId: number,
    employeeId: number,
    photoUrl: string
  ) {
    return await this.uploadBeforePhotos(orderId, employeeId, [photoUrl]);
  }

  /**
   * 上传服务后照片（支持多张，最多9张）
   */
  async uploadAfterPhotos(
    orderId: number,
    employeeId: number,
    photoUrls: string[]
  ) {
    if (!photoUrls || photoUrls.length === 0) {
      throw new CustomError('照片链接不能为空');
    }

    if (photoUrls.length > 9) {
      throw new CustomError('服务后照片最多只能上传9张');
    }

    // 对新上传的照片进行去重
    const uniqueNewPhotos = [...new Set(photoUrls)];

    // 查找是否已存在记录
    const servicePhoto = await this.findOne({
      where: { orderId, employeeId },
    });

    if (servicePhoto) {
      // 合并现有照片和新照片，并去重
      const existingPhotos = servicePhoto.afterPhotos || [];
      const allPhotos = [...new Set([...existingPhotos, ...uniqueNewPhotos])];

      if (allPhotos.length > 9) {
        throw new CustomError(
          `服务后照片总数不能超过9张，当前已有${existingPhotos.length}张，新增${uniqueNewPhotos.length}张，去重后总计${allPhotos.length}张`
        );
      }

      // 检查是否有新照片被添加
      const newPhotosAdded = allPhotos.length > existingPhotos.length;
      if (!newPhotosAdded) {
        return servicePhoto;
      }

      // 更新现有记录
      await servicePhoto.update({
        afterPhotos: allPhotos,
        afterPhotoTime: new Date(),
      });
      return servicePhoto;
    } else {
      // 创建新记录
      return await this.create({
        orderId,
        employeeId,
        afterPhotos: uniqueNewPhotos,
        afterPhotoTime: new Date(),
      });
    }
  }

  /**
   * 上传单张服务后照片（兼容性方法）
   */
  async uploadAfterPhoto(
    orderId: number,
    employeeId: number,
    photoUrl: string
  ) {
    return await this.uploadAfterPhotos(orderId, employeeId, [photoUrl]);
  }

  /**
   * 检查订单是否已上传服务前照片
   */
  async hasBeforePhoto(orderId: number, employeeId: number): Promise<boolean> {
    const servicePhoto = await this.findOne({
      where: { orderId, employeeId },
    });
    return !!(
      servicePhoto &&
      servicePhoto.beforePhotos &&
      servicePhoto.beforePhotos.length > 0
    );
  }

  /**
   * 检查订单是否已上传服务后照片
   */
  async hasAfterPhoto(orderId: number, employeeId: number): Promise<boolean> {
    const servicePhoto = await this.findOne({
      where: { orderId, employeeId },
    });
    return !!(
      servicePhoto &&
      servicePhoto.afterPhotos &&
      servicePhoto.afterPhotos.length > 0
    );
  }

  /**
   * 获取服务前照片数量
   */
  async getBeforePhotoCount(
    orderId: number,
    employeeId: number
  ): Promise<number> {
    const servicePhoto = await this.findOne({
      where: { orderId, employeeId },
    });
    return servicePhoto?.beforePhotos?.length || 0;
  }

  /**
   * 获取服务后照片数量
   */
  async getAfterPhotoCount(
    orderId: number,
    employeeId: number
  ): Promise<number> {
    const servicePhoto = await this.findOne({
      where: { orderId, employeeId },
    });
    return servicePhoto?.afterPhotos?.length || 0;
  }

  /**
   * 获取员工的服务照片列表
   */
  async findByEmployeeId(employeeId: number, page = 1, pageSize = 10) {
    const offset = (page - 1) * pageSize;
    return await this.findAll({
      query: { employeeId },
      offset,
      limit: pageSize,
      include: [Order],
      order: [['createdAt', 'DESC']],
    });
  }

  /**
   * 删除指定的服务前照片
   */
  async removeBeforePhotos(
    orderId: number,
    employeeId: number,
    photoUrls: string[]
  ) {
    const servicePhoto = await this.findOne({
      where: { orderId, employeeId },
    });

    if (!servicePhoto || !servicePhoto.beforePhotos) {
      throw new CustomError('未找到服务前照片记录');
    }

    // 过滤掉要删除的照片
    const remainingPhotos = servicePhoto.beforePhotos.filter(
      photo => !photoUrls.includes(photo)
    );

    await servicePhoto.update({
      beforePhotos: remainingPhotos.length > 0 ? remainingPhotos : null,
      beforePhotoTime: remainingPhotos.length > 0 ? new Date() : null,
    });

    return servicePhoto;
  }

  /**
   * 删除指定的服务后照片
   */
  async removeAfterPhotos(
    orderId: number,
    employeeId: number,
    photoUrls: string[]
  ) {
    const servicePhoto = await this.findOne({
      where: { orderId, employeeId },
    });

    if (!servicePhoto || !servicePhoto.afterPhotos) {
      throw new CustomError('未找到服务后照片记录');
    }

    // 过滤掉要删除的照片
    const remainingPhotos = servicePhoto.afterPhotos.filter(
      photo => !photoUrls.includes(photo)
    );

    await servicePhoto.update({
      afterPhotos: remainingPhotos.length > 0 ? remainingPhotos : null,
      afterPhotoTime: remainingPhotos.length > 0 ? new Date() : null,
    });

    return servicePhoto;
  }

  /**
   * 获取照片统计信息
   */
  async getPhotoStatistics(orderId: number, employeeId: number) {
    const servicePhoto = await this.findOne({
      where: { orderId, employeeId },
    });

    const beforeCount = servicePhoto?.beforePhotos?.length || 0;
    const afterCount = servicePhoto?.afterPhotos?.length || 0;

    return {
      beforePhotos: {
        count: beforeCount,
        maxCount: 9,
        remaining: 9 - beforeCount,
        photos: servicePhoto?.beforePhotos || [],
      },
      afterPhotos: {
        count: afterCount,
        maxCount: 9,
        remaining: 9 - afterCount,
        photos: servicePhoto?.afterPhotos || [],
      },
      totalPhotos: beforeCount + afterCount,
      lastUpdated: servicePhoto?.updatedAt,
    };
  }
}
