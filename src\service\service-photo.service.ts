import { Provide } from '@midwayjs/core';
import { ServicePhoto } from '../entity/service-photo.entity';
import { BaseService } from '../common/BaseService';
import { Order } from '../entity/order.entity';
import { Employee } from '../entity/employee.entity';
import { CustomError } from '../error/custom.error';

@Provide()
export class ServicePhotoService extends BaseService<ServicePhoto> {
  constructor() {
    super('服务照片');
  }

  getModel() {
    return ServicePhoto;
  }

  /**
   * 根据订单ID查找服务照片
   */
  async findByOrderId(orderId: number) {
    return await this.findOne({
      where: { orderId },
      include: [Order, Employee],
    });
  }

  /**
   * 上传服务前照片（支持多张，最多9张）
   */
  async uploadBeforePhotos(
    orderId: number,
    employeeId: number,
    photoUrls: string[]
  ) {
    if (!photoUrls || photoUrls.length === 0) {
      throw new CustomError('照片链接不能为空');
    }

    if (photoUrls.length > 9) {
      throw new CustomError('服务前照片最多只能上传9张');
    }

    // 查找是否已存在记录
    const servicePhoto = await this.findOne({
      where: { orderId, employeeId },
    });

    if (servicePhoto) {
      // 合并现有照片和新照片
      const existingPhotos = servicePhoto.beforePhotos || [];
      const allPhotos = [...existingPhotos, ...photoUrls];

      if (allPhotos.length > 9) {
        throw new CustomError('服务前照片总数不能超过9张');
      }

      // 更新现有记录
      await servicePhoto.update({
        beforePhotos: allPhotos,
        beforePhotoTime: new Date(),
      });
      return servicePhoto;
    } else {
      // 创建新记录
      return await this.create({
        orderId,
        employeeId,
        beforePhotos: photoUrls,
        beforePhotoTime: new Date(),
      });
    }
  }

  /**
   * 上传单张服务前照片（兼容性方法）
   */
  async uploadBeforePhoto(
    orderId: number,
    employeeId: number,
    photoUrl: string
  ) {
    return await this.uploadBeforePhotos(orderId, employeeId, [photoUrl]);
  }

  /**
   * 上传服务后照片（支持多张，最多9张）
   */
  async uploadAfterPhotos(
    orderId: number,
    employeeId: number,
    photoUrls: string[]
  ) {
    if (!photoUrls || photoUrls.length === 0) {
      throw new CustomError('照片链接不能为空');
    }

    if (photoUrls.length > 9) {
      throw new CustomError('服务后照片最多只能上传9张');
    }

    // 查找是否已存在记录
    const servicePhoto = await this.findOne({
      where: { orderId, employeeId },
    });

    if (servicePhoto) {
      // 合并现有照片和新照片
      const existingPhotos = servicePhoto.afterPhotos || [];
      const allPhotos = [...existingPhotos, ...photoUrls];

      if (allPhotos.length > 9) {
        throw new CustomError('服务后照片总数不能超过9张');
      }

      // 更新现有记录
      await servicePhoto.update({
        afterPhotos: allPhotos,
        afterPhotoTime: new Date(),
      });
      return servicePhoto;
    } else {
      // 创建新记录
      return await this.create({
        orderId,
        employeeId,
        afterPhotos: photoUrls,
        afterPhotoTime: new Date(),
      });
    }
  }

  /**
   * 上传单张服务后照片（兼容性方法）
   */
  async uploadAfterPhoto(
    orderId: number,
    employeeId: number,
    photoUrl: string
  ) {
    return await this.uploadAfterPhotos(orderId, employeeId, [photoUrl]);
  }

  /**
   * 检查订单是否已上传服务前照片
   */
  async hasBeforePhoto(orderId: number, employeeId: number): Promise<boolean> {
    const servicePhoto = await this.findOne({
      where: { orderId, employeeId },
    });
    return !!(
      servicePhoto &&
      servicePhoto.beforePhotos &&
      servicePhoto.beforePhotos.length > 0
    );
  }

  /**
   * 检查订单是否已上传服务后照片
   */
  async hasAfterPhoto(orderId: number, employeeId: number): Promise<boolean> {
    const servicePhoto = await this.findOne({
      where: { orderId, employeeId },
    });
    return !!(
      servicePhoto &&
      servicePhoto.afterPhotos &&
      servicePhoto.afterPhotos.length > 0
    );
  }

  /**
   * 获取服务前照片数量
   */
  async getBeforePhotoCount(
    orderId: number,
    employeeId: number
  ): Promise<number> {
    const servicePhoto = await this.findOne({
      where: { orderId, employeeId },
    });
    return servicePhoto?.beforePhotos?.length || 0;
  }

  /**
   * 获取服务后照片数量
   */
  async getAfterPhotoCount(
    orderId: number,
    employeeId: number
  ): Promise<number> {
    const servicePhoto = await this.findOne({
      where: { orderId, employeeId },
    });
    return servicePhoto?.afterPhotos?.length || 0;
  }

  /**
   * 获取员工的服务照片列表
   */
  async findByEmployeeId(employeeId: number, page = 1, pageSize = 10) {
    const offset = (page - 1) * pageSize;
    return await this.findAll({
      query: { employeeId },
      offset,
      limit: pageSize,
      include: [Order],
      order: [['createdAt', 'DESC']],
    });
  }
}
