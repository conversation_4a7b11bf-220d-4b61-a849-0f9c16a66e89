# 员工服务拍照功能 API 文档

## 概述

员工在服务过程中可以拍摄洗护前后的照片，用于记录服务质量和过程。支持服务前最多9张照片，服务后最多9张照片。本文档描述了相关的API接口。

## 业务流程

1. **员工接单** - 订单状态变为"待服务"
2. **员工出发** - 订单状态变为"已出发"
3. **开始服务** - 订单状态变为"服务中"，可选择上传服务前照片
4. **完成服务** - 订单状态变为"已完成"，可选择上传服务后照片

## API 接口

### 1. 开始服务（支持上传服务前照片）

**接口地址：** `POST /orders/{orderId}/start`

**请求参数：**
```json
{
  "employeeId": 123,
  "beforePhotos": [  // 可选，最多9张
    "https://example.com/before-photo1.jpg",
    "https://example.com/before-photo2.jpg"
  ]
}
```

**响应：**
```json
{
  "success": true,
  "data": true
}
```

### 2. 完成订单（支持上传服务后照片）

**接口地址：** `POST /orders/{orderId}/complete`

**请求参数：**
```json
{
  "employeeId": 123,
  "afterPhotos": [  // 可选，最多9张
    "https://example.com/after-photo1.jpg",
    "https://example.com/after-photo2.jpg"
  ]
}
```

**响应：**
```json
{
  "success": true,
  "data": true
}
```

### 3. 批量上传服务前照片

**接口地址：** `POST /orders/{orderId}/upload-before-photos`

**请求参数：**
```json
{
  "employeeId": 123,
  "photoUrls": [  // 最多9张，会与已有照片合并
    "https://example.com/before-photo1.jpg",
    "https://example.com/before-photo2.jpg",
    "https://example.com/before-photo3.jpg"
  ]
}
```

**响应：**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "orderId": 456,
    "employeeId": 123,
    "beforePhotos": [
      "https://example.com/before-photo1.jpg",
      "https://example.com/before-photo2.jpg",
      "https://example.com/before-photo3.jpg"
    ],
    "beforePhotoTime": "2024-01-01T10:00:00.000Z",
    "afterPhotos": null,
    "afterPhotoTime": null,
    "createdAt": "2024-01-01T09:00:00.000Z",
    "updatedAt": "2024-01-01T10:00:00.000Z"
  }
}
```

### 4. 批量上传服务后照片

**接口地址：** `POST /orders/{orderId}/upload-after-photos`

**请求参数：**
```json
{
  "employeeId": 123,
  "photoUrls": [  // 最多9张，会与已有照片合并
    "https://example.com/after-photo1.jpg",
    "https://example.com/after-photo2.jpg"
  ]
}
```

**响应：**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "orderId": 456,
    "employeeId": 123,
    "beforePhotos": [
      "https://example.com/before-photo1.jpg",
      "https://example.com/before-photo2.jpg",
      "https://example.com/before-photo3.jpg"
    ],
    "beforePhotoTime": "2024-01-01T10:00:00.000Z",
    "afterPhotos": [
      "https://example.com/after-photo1.jpg",
      "https://example.com/after-photo2.jpg"
    ],
    "afterPhotoTime": "2024-01-01T11:00:00.000Z",
    "createdAt": "2024-01-01T09:00:00.000Z",
    "updatedAt": "2024-01-01T11:00:00.000Z"
  }
}
```

### 5. 单独上传服务前照片（兼容性接口）

**接口地址：** `POST /orders/{orderId}/upload-before-photo`

**请求参数：**
```json
{
  "employeeId": 123,
  "photoUrl": "https://example.com/before-photo.jpg"
}
```

### 6. 单独上传服务后照片（兼容性接口）

**接口地址：** `POST /orders/{orderId}/upload-after-photo`

**请求参数：**
```json
{
  "employeeId": 123,
  "photoUrl": "https://example.com/after-photo.jpg"
}
```

### 7. 查询订单服务照片

**接口地址：** `GET /orders/{orderId}/service-photos`

**响应：**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "orderId": 456,
    "employeeId": 123,
    "beforePhotos": [
      "https://example.com/before-photo1.jpg",
      "https://example.com/before-photo2.jpg",
      "https://example.com/before-photo3.jpg"
    ],
    "beforePhotoTime": "2024-01-01T10:00:00.000Z",
    "afterPhotos": [
      "https://example.com/after-photo1.jpg",
      "https://example.com/after-photo2.jpg"
    ],
    "afterPhotoTime": "2024-01-01T11:00:00.000Z",
    "order": {
      "id": 456,
      "sn": "1704096000001234",
      "status": "已完成"
    },
    "employee": {
      "id": 123,
      "name": "张三",
      "phone": "13800138000"
    }
  }
}
```

### 8. 服务照片管理接口

#### 8.1 查询服务照片列表

**接口地址：** `GET /service-photos`

**查询参数：**
- `current`: 当前页码（默认1）
- `pageSize`: 每页数量（默认10）
- `orderId`: 订单ID（可选）
- `employeeId`: 员工ID（可选）

#### 8.2 查询员工的服务照片

**接口地址：** `GET /service-photos/employee/{employeeId}`

**查询参数：**
- `current`: 当前页码（默认1）
- `pageSize`: 每页数量（默认10）

#### 8.3 检查照片上传状态

**检查服务前照片：** `GET /service-photos/check/{orderId}/{employeeId}/before`

**检查服务后照片：** `GET /service-photos/check/{orderId}/{employeeId}/after`

**响应：**
```json
{
  "success": true,
  "data": {
    "hasPhoto": true
  }
}
```

#### 8.4 查询照片数量

**查询服务前照片数量：** `GET /service-photos/count/{orderId}/{employeeId}/before`

**查询服务后照片数量：** `GET /service-photos/count/{orderId}/{employeeId}/after`

**响应：**
```json
{
  "success": true,
  "data": {
    "count": 3
  }
}
```

#### 8.5 批量上传接口

**批量上传服务前照片：** `POST /service-photos/upload-before-photos`

**批量上传服务后照片：** `POST /service-photos/upload-after-photos`

**请求参数：**
```json
{
  "orderId": 456,
  "employeeId": 123,
  "photoUrls": [
    "https://example.com/photo1.jpg",
    "https://example.com/photo2.jpg"
  ]
}
```

## 数据库表结构

### service_photos 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int(11) | 主键ID |
| orderId | int(11) | 关联订单ID |
| employeeId | int(11) | 关联员工ID |
| beforePhotos | json | 服务前照片链接数组，最多9张 |
| beforePhotoTime | datetime | 服务前照片上传时间 |
| afterPhotos | json | 服务后照片链接数组，最多9张 |
| afterPhotoTime | datetime | 服务后照片上传时间 |
| createdAt | datetime | 创建时间 |
| updatedAt | datetime | 更新时间 |

## 注意事项

1. 照片上传功能是可选的，不强制要求员工必须上传照片
2. 照片链接由前端调用云存储服务获得，后端只存储链接地址
3. 每个订单每个员工只能有一条服务照片记录
4. 服务前最多支持9张照片，服务后最多支持9张照片
5. 支持分批上传，新上传的照片会与已有照片合并，但总数不能超过9张
6. 服务前后照片可以分别上传，支持先上传几张，后续再补充其他照片
7. 照片记录会随订单删除而级联删除
8. 员工删除时，照片记录的员工ID会被设置为NULL，但照片记录保留
9. 提供了兼容性接口，支持单张照片上传（内部转换为数组处理）
10. 照片数组存储为JSON格式，便于查询和处理
