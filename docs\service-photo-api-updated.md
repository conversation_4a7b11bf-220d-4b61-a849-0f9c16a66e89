# 员工服务拍照功能 API 文档（支持去重）

## 概述

员工在服务过程中可以拍摄洗护前后的照片，用于记录服务质量和过程。支持服务前最多9张照片，服务后最多9张照片。**系统自动进行照片去重，避免重复上传相同照片**。本文档描述了相关的API接口。

## 🔄 去重机制

### 自动去重特性
1. **新照片内部去重**：上传的照片数组会自动去除重复项
2. **与已有照片去重**：新照片与已存在的照片进行去重比较
3. **智能错误提示**：如果所有照片都已存在，会提示"无需重复上传"
4. **详细数量统计**：错误信息包含当前数量、新增数量、去重后总数

### 去重示例
```json
// 第一次上传
POST /orders/123/upload-before-photos
{
  "employeeId": 456,
  "photoUrls": ["photo1.jpg", "photo2.jpg", "photo1.jpg"] // photo1.jpg重复
}
// 结果：实际存储 ["photo1.jpg", "photo2.jpg"]

// 第二次上传
POST /orders/123/upload-before-photos  
{
  "employeeId": 456,
  "photoUrls": ["photo2.jpg", "photo3.jpg"] // photo2.jpg已存在
}
// 结果：实际存储 ["photo1.jpg", "photo2.jpg", "photo3.jpg"]
// 只有photo3.jpg是新增的
```

## 业务流程

1. **员工接单** - 订单状态变为"待服务"
2. **员工出发** - 订单状态变为"已出发"
3. **开始服务** - 订单状态变为"服务中"，可选择上传服务前照片（最多9张）
4. **完成服务** - 订单状态变为"已完成"，可选择上传服务后照片（最多9张）

## API 接口

### 1. 开始服务（支持上传服务前照片）

**接口地址：** `POST /orders/{orderId}/start`

**请求参数：**
```json
{
  "employeeId": 123,
  "beforePhotos": [  // 可选，最多9张，自动去重
    "https://example.com/before-photo1.jpg",
    "https://example.com/before-photo2.jpg"
  ]
}
```

### 2. 完成订单（支持上传服务后照片）

**接口地址：** `POST /orders/{orderId}/complete`

**请求参数：**
```json
{
  "employeeId": 123,
  "afterPhotos": [  // 可选，最多9张，自动去重
    "https://example.com/after-photo1.jpg",
    "https://example.com/after-photo2.jpg"
  ]
}
```

### 3. 批量上传服务前照片（推荐）

**接口地址：** `POST /orders/{orderId}/upload-before-photos`

**请求参数：**
```json
{
  "employeeId": 123,
  "photoUrls": [  // 最多9张，自动去重并与已有照片合并
    "https://example.com/before-photo1.jpg",
    "https://example.com/before-photo2.jpg",
    "https://example.com/before-photo3.jpg"
  ]
}
```

**成功响应：**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "orderId": 456,
    "employeeId": 123,
    "beforePhotos": [
      "https://example.com/before-photo1.jpg",
      "https://example.com/before-photo2.jpg",
      "https://example.com/before-photo3.jpg"
    ],
    "beforePhotoTime": "2024-01-01T10:00:00.000Z"
  }
}
```

**去重错误响应：**
```json
{
  "success": false,
  "message": "所有照片都已存在，无需重复上传"
}
```

**数量超限错误响应：**
```json
{
  "success": false,
  "message": "服务前照片总数不能超过9张，当前已有6张，新增4张，去重后总计10张"
}
```

### 4. 批量上传服务后照片

**接口地址：** `POST /orders/{orderId}/upload-after-photos`

**请求参数：**
```json
{
  "employeeId": 123,
  "photoUrls": [  // 最多9张，自动去重并与已有照片合并
    "https://example.com/after-photo1.jpg",
    "https://example.com/after-photo2.jpg"
  ]
}
```

### 5. 照片管理接口

#### 5.1 删除服务前照片

**接口地址：** `POST /service-photos/remove-before-photos`

**请求参数：**
```json
{
  "orderId": 456,
  "employeeId": 123,
  "photoUrls": [
    "https://example.com/before-photo1.jpg",
    "https://example.com/before-photo2.jpg"
  ]
}
```

#### 5.2 删除服务后照片

**接口地址：** `POST /service-photos/remove-after-photos`

**请求参数：**
```json
{
  "orderId": 456,
  "employeeId": 123,
  "photoUrls": [
    "https://example.com/after-photo1.jpg"
  ]
}
```

#### 5.3 获取照片统计信息

**接口地址：** `GET /service-photos/statistics/{orderId}/{employeeId}`

**响应：**
```json
{
  "success": true,
  "data": {
    "beforePhotos": {
      "count": 3,
      "maxCount": 9,
      "remaining": 6,
      "photos": ["photo1.jpg", "photo2.jpg", "photo3.jpg"]
    },
    "afterPhotos": {
      "count": 2,
      "maxCount": 9,
      "remaining": 7,
      "photos": ["photo4.jpg", "photo5.jpg"]
    },
    "totalPhotos": 5,
    "lastUpdated": "2024-01-01T11:00:00.000Z"
  }
}
```

### 6. 查询接口

#### 6.1 查询订单服务照片

**接口地址：** `GET /orders/{orderId}/service-photos`

#### 6.2 查询照片数量

**服务前照片数量：** `GET /service-photos/count/{orderId}/{employeeId}/before`

**服务后照片数量：** `GET /service-photos/count/{orderId}/{employeeId}/after`

## 🛡️ 去重和验证规则

### 1. 去重规则
- **URL完全匹配**：基于照片URL进行精确匹配去重
- **大小写敏感**：URL区分大小写
- **自动合并**：新照片与已有照片自动合并去重

### 2. 数量限制
- **单次上传**：最多9张照片
- **总数限制**：服务前/后各最多9张照片
- **智能提示**：超限时提供详细的数量信息

### 3. 错误处理
- **重复上传**：所有照片都已存在时提示无需重复上传
- **数量超限**：提供当前数量、新增数量、去重后总数的详细信息
- **参数验证**：完善的参数校验和错误提示

## 注意事项

1. **自动去重**：系统会自动去除重复照片，无需手动处理
2. **增量上传**：支持分批上传，新照片会与已有照片智能合并
3. **URL精确匹配**：去重基于照片URL的完全匹配
4. **友好提示**：提供详细的错误信息和数量统计
5. **数据安全**：照片删除操作会保留其他照片
6. **性能优化**：使用Set数据结构进行高效去重
