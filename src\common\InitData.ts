import {
  AreaAttributes,
  DictionaryAttributes,
  FeatureAttributes,
  PermissionAttributes,
  MessageTemplateAttributes,
} from '../entity';

/** 行政区划初始数据 */
export const initData_areas: Omit<AreaAttributes, 'id'>[] = [
  { code: '610000', name: '陕西省', parentCode: null },
  { code: '610100', name: '西安市', parentCode: '610000' },
  { code: '610101', name: '市辖区', parentCode: '610100' },
  { code: '610102', name: '新城区', parentCode: '610100' },
  { code: '610103', name: '碑林区', parentCode: '610100' },
  { code: '610104', name: '莲湖区', parentCode: '610100' },
  { code: '610111', name: '灞桥区', parentCode: '610100' },
  { code: '610112', name: '未央区', parentCode: '610100' },
  { code: '610113', name: '雁塔区', parentCode: '610100' },
  { code: '610114', name: '阎良区', parentCode: '610100' },
  { code: '610115', name: '临潼区', parentCode: '610100' },
  { code: '610116', name: '长安区', parentCode: '610100' },
  { code: '610117', name: '西咸新区', parentCode: '610100' },
  { code: '610122', name: '蓝田县', parentCode: '610100' },
  { code: '610124', name: '周至县', parentCode: '610100' },
  { code: '610125', name: '鄠邑区', parentCode: '610100' },
  { code: '610126', name: '高陵区', parentCode: '610100' },
  { code: '610127', name: '经开区', parentCode: '610100' },
  { code: '610128', name: '高新区', parentCode: '610100' },
  { code: '610129', name: '曲江新区', parentCode: '610100' },
  { code: '610130', name: '浐灞生态区', parentCode: '610100' },
  { code: '610131', name: '航天基地', parentCode: '610100' },
  { code: '610132', name: '航空基地', parentCode: '610100' },
  { code: '610133', name: '国际港务区', parentCode: '610100' },
  { code: '610200', name: '铜川市', parentCode: '610000' },
  { code: '610201', name: '市辖区', parentCode: '610200' },
  { code: '610202', name: '王益区', parentCode: '610200' },
  { code: '610203', name: '印台区', parentCode: '610200' },
  { code: '610204', name: '耀州区', parentCode: '610200' },
  { code: '610222', name: '宜君县', parentCode: '610200' },
  { code: '610300', name: '宝鸡市', parentCode: '610000' },
  { code: '610301', name: '市辖区', parentCode: '610300' },
  { code: '610302', name: '渭滨区', parentCode: '610300' },
  { code: '610303', name: '金台区', parentCode: '610300' },
  { code: '610304', name: '陈仓区', parentCode: '610300' },
  { code: '610322', name: '凤翔县', parentCode: '610300' },
  { code: '610323', name: '岐山县', parentCode: '610300' },
  { code: '610324', name: '扶风县', parentCode: '610300' },
  { code: '610326', name: '眉县', parentCode: '610300' },
  { code: '610327', name: '陇县', parentCode: '610300' },
  { code: '610328', name: '千阳县', parentCode: '610300' },
  { code: '610329', name: '麟游县', parentCode: '610300' },
  { code: '610330', name: '凤县', parentCode: '610300' },
  { code: '610331', name: '太白县', parentCode: '610300' },
  { code: '610400', name: '咸阳市', parentCode: '610000' },
  { code: '610401', name: '市辖区', parentCode: '610400' },
  { code: '610402', name: '秦都区', parentCode: '610400' },
  { code: '610403', name: '杨陵区', parentCode: '610400' },
  { code: '610404', name: '渭城区', parentCode: '610400' },
  { code: '610422', name: '三原县', parentCode: '610400' },
  { code: '610423', name: '泾阳县', parentCode: '610400' },
  { code: '610424', name: '乾县', parentCode: '610400' },
  { code: '610425', name: '礼泉县', parentCode: '610400' },
  { code: '610426', name: '永寿县', parentCode: '610400' },
  { code: '610427', name: '彬县', parentCode: '610400' },
  { code: '610428', name: '长武县', parentCode: '610400' },
  { code: '610429', name: '旬邑县', parentCode: '610400' },
  { code: '610430', name: '淳化县', parentCode: '610400' },
  { code: '610431', name: '武功县', parentCode: '610400' },
  { code: '610481', name: '兴平市', parentCode: '610400' },
  { code: '610500', name: '渭南市', parentCode: '610000' },
  { code: '610501', name: '市辖区', parentCode: '610500' },
  { code: '610502', name: '临渭区', parentCode: '610500' },
  { code: '610521', name: '华县', parentCode: '610500' },
  { code: '610522', name: '潼关县', parentCode: '610500' },
  { code: '610523', name: '大荔县', parentCode: '610500' },
  { code: '610524', name: '合阳县', parentCode: '610500' },
  { code: '610525', name: '澄城县', parentCode: '610500' },
  { code: '610526', name: '蒲城县', parentCode: '610500' },
  { code: '610527', name: '白水县', parentCode: '610500' },
  { code: '610528', name: '富平县', parentCode: '610500' },
  { code: '610581', name: '韩城市', parentCode: '610500' },
  { code: '610582', name: '华阴市', parentCode: '610500' },
  { code: '610600', name: '延安市', parentCode: '610000' },
  { code: '610601', name: '市辖区', parentCode: '610600' },
  { code: '610602', name: '宝塔区', parentCode: '610600' },
  { code: '610621', name: '延长县', parentCode: '610600' },
  { code: '610622', name: '延川县', parentCode: '610600' },
  { code: '610623', name: '子长县', parentCode: '610600' },
  { code: '610624', name: '安塞县', parentCode: '610600' },
  { code: '610625', name: '志丹县', parentCode: '610600' },
  { code: '610626', name: '吴起县', parentCode: '610600' },
  { code: '610627', name: '甘泉县', parentCode: '610600' },
  { code: '610628', name: '富县', parentCode: '610600' },
  { code: '610629', name: '洛川县', parentCode: '610600' },
  { code: '610630', name: '宜川县', parentCode: '610600' },
  { code: '610631', name: '黄龙县', parentCode: '610600' },
  { code: '610632', name: '黄陵县', parentCode: '610600' },
  { code: '610700', name: '汉中市', parentCode: '610000' },
  { code: '610701', name: '市辖区', parentCode: '610700' },
  { code: '610702', name: '汉台区', parentCode: '610700' },
  { code: '610721', name: '南郑县', parentCode: '610700' },
  { code: '610722', name: '城固县', parentCode: '610700' },
  { code: '610723', name: '洋县', parentCode: '610700' },
  { code: '610724', name: '西乡县', parentCode: '610700' },
  { code: '610725', name: '勉县', parentCode: '610700' },
  { code: '610726', name: '宁强县', parentCode: '610700' },
  { code: '610727', name: '略阳县', parentCode: '610700' },
  { code: '610728', name: '镇巴县', parentCode: '610700' },
  { code: '610729', name: '留坝县', parentCode: '610700' },
  { code: '610730', name: '佛坪县', parentCode: '610700' },
  { code: '610800', name: '榆林市', parentCode: '610000' },
  { code: '610801', name: '市辖区', parentCode: '610800' },
  { code: '610802', name: '榆阳区', parentCode: '610800' },
  { code: '610821', name: '神木县', parentCode: '610800' },
  { code: '610822', name: '府谷县', parentCode: '610800' },
  { code: '610823', name: '横山县', parentCode: '610800' },
  { code: '610824', name: '靖边县', parentCode: '610800' },
  { code: '610825', name: '定边县', parentCode: '610800' },
  { code: '610826', name: '绥德县', parentCode: '610800' },
  { code: '610827', name: '米脂县', parentCode: '610800' },
  { code: '610828', name: '佳县', parentCode: '610800' },
  { code: '610829', name: '吴堡县', parentCode: '610800' },
  { code: '610830', name: '清涧县', parentCode: '610800' },
  { code: '610831', name: '子洲县', parentCode: '610800' },
  { code: '610900', name: '安康市', parentCode: '610000' },
  { code: '610901', name: '市辖区', parentCode: '610900' },
  { code: '610902', name: '汉滨区', parentCode: '610900' },
  { code: '610921', name: '汉阴县', parentCode: '610900' },
  { code: '610922', name: '石泉县', parentCode: '610900' },
  { code: '610923', name: '宁陕县', parentCode: '610900' },
  { code: '610924', name: '紫阳县', parentCode: '610900' },
  { code: '610925', name: '岚皋县', parentCode: '610900' },
  { code: '610926', name: '平利县', parentCode: '610900' },
  { code: '610927', name: '镇坪县', parentCode: '610900' },
  { code: '610928', name: '旬阳县', parentCode: '610900' },
  { code: '610929', name: '白河县', parentCode: '610900' },
  { code: '611000', name: '商洛市', parentCode: '610000' },
  { code: '611001', name: '市辖区', parentCode: '611000' },
  { code: '611002', name: '商州区', parentCode: '611000' },
  { code: '611021', name: '洛南县', parentCode: '611000' },
  { code: '611022', name: '丹凤县', parentCode: '611000' },
  { code: '611023', name: '商南县', parentCode: '611000' },
  { code: '611024', name: '山阳县', parentCode: '611000' },
  { code: '611025', name: '镇安县', parentCode: '611000' },
  { code: '611026', name: '柞水县', parentCode: '611000' },
];

/** 系统功能点 */
export const initData_feature: Omit<FeatureAttributes, 'id' | 'permissions'>[] =
  [
    { code: '010000', name: '车辆管理', path: '', orderIndex: 1 },
    { code: '020000', name: '人员管理', path: '', orderIndex: 2 },
    {
      code: '020100',
      name: '员工信息管理',
      path: '',
      parentCode: '020000',
      orderIndex: 1,
    },
    {
      code: '020200',
      name: '用户数据管理',
      path: '',
      parentCode: '020000',
      orderIndex: 2,
    },
    { code: '030000', name: '预约管理', path: '', orderIndex: 3 },
    { code: '040000', name: '数据分析', path: '', orderIndex: 4 },
    { code: '050000', name: '小程序模块管理', path: '', orderIndex: 5 },
    {
      code: '050100',
      name: '轮播图',
      path: '',
      parentCode: '050000',
      orderIndex: 1,
    },
    {
      code: '050200',
      name: '内容模板',
      path: '',
      parentCode: '050000',
      orderIndex: 2,
    },
    {
      code: '050300',
      name: '照片墙',
      path: '',
      parentCode: '050000',
      orderIndex: 3,
    },
    {
      code: '050400',
      name: '活动页',
      path: '',
      parentCode: '050000',
      orderIndex: 4,
    },
    { code: '060000', name: '投诉管理', path: '', orderIndex: 6 },
    { code: '070000', name: '考勤管理', path: '', orderIndex: 7 },
    { code: '080000', name: '服务管理', path: '', orderIndex: 8 },
    {
      code: '080100',
      name: '服务类目',
      path: '',
      parentCode: '080000',
      orderIndex: 1,
    },
    {
      code: '080200',
      name: '服务项目',
      path: '',
      parentCode: '080000',
      orderIndex: 2,
    },
    { code: '090000', name: '卡券管理', path: '', orderIndex: 9 },
    {
      code: '090100',
      name: '权益卡管理',
      path: '',
      parentCode: '090000',
      orderIndex: 1,
    },
    {
      code: '090200',
      name: '代金券管理',
      path: '',
      parentCode: '090000',
      orderIndex: 2,
    },
    { code: '980000', name: '基础数据管理', path: '', orderIndex: 98 },
    {
      code: '980100',
      name: '数据字典',
      path: '',
      parentCode: '980000',
      orderIndex: 1,
    },
    {
      code: '980200',
      name: '区域管理',
      path: '',
      parentCode: '980000',
      orderIndex: 2,
    },
    { code: '990000', name: '系统设置', path: '', orderIndex: 99 },
    {
      code: '990100',
      name: '用户管理',
      path: '',
      parentCode: '990000',
      orderIndex: 1,
    },
    {
      code: '990200',
      name: '角色管理',
      path: '',
      parentCode: '990000',
      orderIndex: 2,
    },
    {
      code: '990300',
      name: '权限点维护',
      path: '',
      parentCode: '990000',
      orderIndex: 3,
    },
    {
      code: '990400',
      name: '系统功能维护',
      path: '',
      parentCode: '990000',
      orderIndex: 4,
    },
  ];

/** 各功能权限点 */
export const initData_permission: Omit<PermissionAttributes, 'id'>[] = [
  { name: '查看', featureCode: '010000', description: '查看车辆信息' },
  { name: '管理', featureCode: '010000', description: '维护车辆信息' },
  { name: '查看', featureCode: '020100', description: '查看员工信息' },
  { name: '管理', featureCode: '020100', description: '维护员工信息' },
  { name: '查看', featureCode: '020200', description: '查看用户数据' },
  { name: '管理', featureCode: '020200', description: '维护用户数据' },
  { name: '查看', featureCode: '030000', description: '查看预约信息' },
  {
    name: '管理',
    featureCode: '030000',
    description: '查看预约信息，执行派单',
  },
  { name: '管理', featureCode: '040000', description: '查看统计分析数据' },
  { name: '查看', featureCode: '050100', description: '查看小程序轮播图' },
  { name: '管理', featureCode: '050100', description: '管理小程序轮播图' },
  { name: '查看', featureCode: '050200', description: '查看小程序内容模板' },
  { name: '管理', featureCode: '050200', description: '管理小程序内容模板' },
  { name: '查看', featureCode: '050300', description: '查看小程序照片墙' },
  { name: '管理', featureCode: '050300', description: '管理小程序照片墙' },
  { name: '查看', featureCode: '050400', description: '查看小程序活动页' },
  { name: '管理', featureCode: '050400', description: '管理小程序活动页' },
  { name: '查看', featureCode: '060000', description: '查看投诉信息' },
  { name: '管理', featureCode: '060000', description: '处理投诉' },
  { name: '管理', featureCode: '070000', description: '查看考勤信息' },
  { name: '查看', featureCode: '080100', description: '查看服务类目' },
  { name: '管理', featureCode: '080100', description: '管理服务类目' },
  { name: '查看', featureCode: '080200', description: '查看服务项目' },
  { name: '管理', featureCode: '080200', description: '管理服务项目' },
  { name: '查看', featureCode: '090100', description: '查看权益卡' },
  { name: '管理', featureCode: '090100', description: '管理权益卡' },
  { name: '查看', featureCode: '090200', description: '查看代金券' },
  { name: '管理', featureCode: '090200', description: '管理代金券' },
  { name: '查看', featureCode: '980100', description: '查看数据字典' },
  { name: '管理', featureCode: '980100', description: '管理数据字典' },
  { name: '查看', featureCode: '980200', description: '查看区域信息' },
  { name: '管理', featureCode: '980200', description: '管理区域信息' },
  { name: '查看', featureCode: '990100', description: '查看系统用户' },
  { name: '管理', featureCode: '990100', description: '管理系统用户' },
  { name: '查看', featureCode: '990200', description: '查看系统角色' },
  { name: '管理', featureCode: '990200', description: '维护系统角色' },
  { name: '查看', featureCode: '990300', description: '查看权限点' },
  { name: '管理', featureCode: '990300', description: '维护权限点' },
  { name: '查看', featureCode: '990400', description: '查看功能点' },
  { name: '管理', featureCode: '990400', description: '维护功能点' },
];

export const initData_dict: Omit<DictionaryAttributes, 'id'>[] = [
  {
    type: '宠物类型',
    code: 'CAT',
    name: '猫',
    sortOrder: 1,
    status: 1,
  },
  {
    type: '宠物类型',
    code: 'DOG',
    name: '狗',
    sortOrder: 2,
    status: 1,
  },
  {
    type: '服务类型',
    code: 'XI_HU',
    name: '洗护',
    sortOrder: 1,
    status: 1,
  },
  {
    type: '服务类型',
    code: 'MEI_RONG',
    name: '美容',
    sortOrder: 2,
    status: 1,
  },
  {
    type: '服务类型',
    code: 'WEI_YANG',
    name: '喂养',
    sortOrder: 3,
    status: 1,
  },
  {
    type: '投诉建议大类',
    code: 'COMPLAINT',
    name: '投诉',
    sortOrder: 1,
    status: 1,
  },
  {
    type: '投诉建议大类',
    code: 'SUGGESTION',
    name: '建议',
    sortOrder: 2,
    status: 1,
  },
  {
    type: '投诉建议小类',
    code: 'ORDER',
    name: '订单投诉',
    sortOrder: 1,
    status: 1,
  },
  {
    type: '投诉建议小类',
    code: 'EMPLOYEE',
    name: '人员投诉',
    sortOrder: 2,
    status: 1,
  },
  {
    type: '投诉建议小类',
    code: 'PLATFORM',
    name: '平台建议',
    sortOrder: 3,
    status: 1,
  },
  {
    type: '投诉建议小类',
    code: 'SERVICE',
    name: '服务建议',
    sortOrder: 4,
    status: 1,
  },
  {
    type: '投诉建议状态',
    code: 'PENDING',
    name: '待处理',
    sortOrder: 1,
    status: 1,
  },
  {
    type: '投诉建议状态',
    code: 'PROCESSING',
    name: '处理中',
    sortOrder: 2,
    status: 1,
  },
  {
    type: '投诉建议状态',
    code: 'RESOLVED',
    name: '已解决',
    sortOrder: 3,
    status: 1,
  },
  {
    type: '投诉建议状态',
    code: 'CLOSED',
    name: '已关闭',
    sortOrder: 4,
    status: 1,
  },
  {
    type: '增项服务类型',
    code: 'JU_BU_HU_LI',
    name: '局部护理',
    sortOrder: 1,
    status: 1,
  },
  {
    type: '增项服务类型',
    code: 'PI_MAO_HU_LI',
    name: '皮毛护理',
    sortOrder: 2,
    status: 1,
  },
  {
    type: '宠物体型',
    code: 'DOG_PUP',
    name: '幼犬',
    description: '体重<2.5kg',
    sortOrder: 1,
    status: 1,
  },
  {
    type: '宠物体型',
    code: 'DOG_SMALL',
    name: '小型犬',
    description: '体重2.5kg~7.5kg',
    sortOrder: 2,
    status: 1,
  },
  {
    type: '宠物体型',
    code: 'DOG_MEDIUM_SMALL',
    name: '中小型犬',
    description: '体重7.5kg~10kg',
    sortOrder: 3,
    status: 1,
  },
  {
    type: '宠物体型',
    code: 'DOG_MEDIUM',
    name: '中型犬',
    description: '体重10kg~15kg',
    sortOrder: 4,
    status: 1,
  },
  {
    type: '宠物体型',
    code: 'DOG_LARGE',
    name: '大型犬',
    description: '体重15kg~25kg',
    sortOrder: 5,
    status: 1,
  },
  {
    type: '宠物体型',
    code: 'DOG_JG_GM',
    name: '特殊犬种（巨贵、古牧）',
    description: '巨贵、古牧',
    sortOrder: 6,
    status: 1,
  },
  {
    type: '宠物体型',
    code: 'DOG_ALSJ',
    name: '特殊犬种（阿拉斯加）',
    description: '阿拉斯加',
    sortOrder: 7,
    status: 1,
  },
  {
    type: '宠物体型',
    code: 'CAT_SMALL',
    name: '小体猫',
    description: '体重<3kg',
    sortOrder: 8,
    status: 1,
  },
  {
    type: '宠物体型',
    code: 'CAT_MEDIUM',
    name: '中体猫',
    description: '体重3kg~6kg',
    sortOrder: 9,
    status: 1,
  },
  {
    type: '宠物体型',
    code: 'CAT_LARGE',
    name: '大体猫',
    description: '体重>6kg',
    sortOrder: 10,
    status: 1,
  },
];

/** 消息模板初始数据 */
export const initData_messageTemplates: Omit<
  MessageTemplateAttributes,
  'id' | 'createdAt' | 'updatedAt'
>[] = [
  {
    code: 'ORDER_NEW',
    type: 'order',
    title: '新订单通知',
    content: '您有新的订单待接单，订单号：{{orderNo}}，客户：{{customerName}}',
    variables: JSON.stringify({ orderNo: '订单号', customerName: '客户姓名' }),
    isActive: true,
  },
  {
    code: 'ORDER_ACCEPTED',
    type: 'order',
    title: '订单已接单',
    content: '订单{{orderNo}}已成功接单，请按时提供服务',
    variables: JSON.stringify({ orderNo: '订单号' }),
    isActive: true,
  },
  {
    code: 'ORDER_COMPLETED',
    type: 'order',
    title: '服务完成通知',
    content: '订单{{orderNo}}服务已完成，感谢您的使用',
    variables: JSON.stringify({ orderNo: '订单号' }),
    isActive: true,
  },
  {
    code: 'SYSTEM_MAINTENANCE',
    type: 'system',
    title: '系统维护通知',
    content: '系统将于{{maintenanceTime}}进行维护，预计耗时{{duration}}',
    variables: JSON.stringify({
      maintenanceTime: '维护时间',
      duration: '维护时长',
    }),
    isActive: true,
  },
  {
    code: 'PLATFORM_POLICY',
    type: 'platform',
    title: '平台政策更新',
    content: '平台服务政策已更新，请及时查看最新内容',
    variables: JSON.stringify({}),
    isActive: true,
  },
];
