-- 创建服务照片表
CREATE TABLE IF NOT EXISTS `service_photos` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '服务照片ID',
  `orderId` int(11) NOT NULL COMMENT '关联订单ID',
  `employeeId` int(11) NOT NULL COMMENT '关联员工ID',
  `beforePhotos` json DEFAULT NULL COMMENT '服务前照片链接数组，最多9张',
  `beforePhotoTime` datetime DEFAULT NULL COMMENT '服务前照片上传时间',
  `afterPhotos` json DEFAULT NULL COMMENT '服务后照片链接数组，最多9张',
  `afterPhotoTime` datetime DEFAULT NULL COMMENT '服务后照片上传时间',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`orderId`),
  KEY `idx_employee_id` (`employeeId`),
  KEY `idx_order_employee` (`orderId`, `employeeId`),
  CONSTRAINT `fk_service_photos_order` FOREIGN KEY (`orderId`) REFERENCES `orders` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_service_photos_employee` FOREIGN KEY (`employeeId`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务照片表';
