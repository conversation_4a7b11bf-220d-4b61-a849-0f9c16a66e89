import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ServicePhotoService } from '../service/service-photo.service';
import { CustomError } from '../error/custom.error';

@Controller('/service-photos')
export class ServicePhotoController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ServicePhotoService;

  @Get('/', { summary: '查询服务照片列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      include: ['order', 'employee'],
      order: [['createdAt', 'DESC']],
    });
  }

  @Get('/:id', { summary: '按ID查询服务照片' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定服务照片');
    }
    return res;
  }

  @Post('/', { summary: '创建服务照片记录' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Del('/:id', { summary: '删除服务照片' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }

  @Get('/order/:orderId', { summary: '查询订单的服务照片' })
  async findByOrder(@Param('orderId') orderId: number) {
    return await this.service.findByOrderId(orderId);
  }

  @Get('/employee/:employeeId', { summary: '查询员工的服务照片列表' })
  async findByEmployee(
    @Param('employeeId') employeeId: number,
    @Query('current') current: number = 1,
    @Query('pageSize') pageSize: number = 10
  ) {
    return await this.service.findByEmployeeId(employeeId, current, pageSize);
  }

  @Post('/upload-before', { summary: '上传服务前照片' })
  async uploadBefore(
    @Body() body: { orderId: number; employeeId: number; photoUrl: string }
  ) {
    if (!body.orderId || !body.employeeId || !body.photoUrl) {
      throw new CustomError('订单ID、员工ID和照片链接都不能为空', 400);
    }
    return await this.service.uploadBeforePhoto(
      body.orderId,
      body.employeeId,
      body.photoUrl
    );
  }

  @Post('/upload-after', { summary: '上传服务后照片' })
  async uploadAfter(
    @Body() body: { orderId: number; employeeId: number; photoUrl: string }
  ) {
    if (!body.orderId || !body.employeeId || !body.photoUrl) {
      throw new CustomError('订单ID、员工ID和照片链接都不能为空', 400);
    }
    return await this.service.uploadAfterPhoto(
      body.orderId,
      body.employeeId,
      body.photoUrl
    );
  }

  @Get('/check/:orderId/:employeeId/before', { summary: '检查是否已上传服务前照片' })
  async checkBeforePhoto(
    @Param('orderId') orderId: number,
    @Param('employeeId') employeeId: number
  ) {
    const hasPhoto = await this.service.hasBeforePhoto(orderId, employeeId);
    return { hasPhoto };
  }

  @Get('/check/:orderId/:employeeId/after', { summary: '检查是否已上传服务后照片' })
  async checkAfterPhoto(
    @Param('orderId') orderId: number,
    @Param('employeeId') employeeId: number
  ) {
    const hasPhoto = await this.service.hasAfterPhoto(orderId, employeeId);
    return { hasPhoto };
  }
}
